#!/bin/bash

# Comprehensive development environment setup for Suna
# This script sets up all the feature flags and configurations needed for development

echo "🛠️  Setting up Suna development environment..."

# Check prerequisites
if ! command -v docker &> /dev/null; then
    echo "❌ Docker not found. Please install Docker first."
    exit 1
fi

if ! docker ps | grep -q "cfox-redis-1"; then
    echo "❌ Redis container not running. Starting Docker containers..."
    docker-compose up -d
    sleep 5
fi

echo "📡 Configuring feature flags..."

# List of feature flags to enable for development
declare -A DEV_FLAGS=(
    ["custom_agents"]="Enable custom agents feature"
    ["agent_marketplace"]="Enable agent marketplace"
    ["mcp_module"]="Enable MCP module"
    ["templates_api"]="Enable templates API"
    ["triggers_api"]="Enable triggers API"
    ["workflows_api"]="Enable workflows API"
    ["knowledge_base"]="Enable knowledge base"
    ["pipedream"]="Enable Pipedream integration"
    ["credentials_api"]="Enable credentials API"
    ["suna_default_agent"]="Enable Suna default agent"
)

# Enable each feature flag
for flag in "${!DEV_FLAGS[@]}"; do
    description="${DEV_FLAGS[$flag]}"
    echo "🔧 Enabling $flag..."
    
    docker exec cfox-redis-1 redis-cli HSET "feature_flag:$flag" \
        enabled true \
        description "$description for dev environment" \
        updated_at "$(date -u +%Y-%m-%dT%H:%M:%SZ)" > /dev/null
    
    docker exec cfox-redis-1 redis-cli SADD feature_flags "$flag" > /dev/null
done

echo "✅ Feature flags configured!"
echo ""
echo "🎉 Development environment is ready!"
echo ""
echo "📋 Enabled features:"
for flag in "${!DEV_FLAGS[@]}"; do
    echo "   • $flag: ${DEV_FLAGS[$flag]}"
done
echo ""
echo "🔄 Please refresh your browser to see all the new features."
echo ""
echo "🌐 Access your application at:"
echo "   • Frontend: http://localhost:3000"
echo "   • Backend API: http://localhost:8000"
echo ""
echo "💡 To reset feature flags, run:"
echo "   docker exec cfox-redis-1 redis-cli FLUSHDB"
